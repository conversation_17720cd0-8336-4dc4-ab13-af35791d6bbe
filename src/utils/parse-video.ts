import type { IVideo } from '../interfaces/video';

export function parseVideo(data: any): IVideo | null {
	if (!data) {
		return null;
	}
	if (!data.id) {
		return null;
	}
	const start = parseInt(data.start);
	if (Number.isNaN(start)) {
		return null;
	}
	const end = parseInt(data.end);
	if (Number.isNaN(end)) {
		return null;
	}

	return {
		id: data.id,
		title: data.title ?? '',
		clips: data.clips ?? [{ start, end, delogoRegions: [] }],  // 向后兼容旧的数据格式，新增delogo字段
		// 新增的可选字段
		uploader: data.uploader,
		uploadDate: data.uploadDate,
		viewCount: data.viewCount,
		duration: data.duration,
		delogoRegions: data.delogoRegions ?? [], // 保留视频级别的delogo区域配置（向后兼容）
		videoWidth: data.videoWidth,
		videoHeight: data.videoHeight
	};
}
