export function secondToTimeString(seconds: number): string {
	if (seconds < 0) {
		return '';
	}

	const hours = Math.floor(seconds / 3600);
	seconds %= 3600;
	const minutes = Math.floor(seconds / 60);
	const remainingSeconds = Math.floor(seconds % 60);

	const pad = (num: number) => num.toString().padStart(2, '0');

	if (hours > 0) {
		return `${hours}:${pad(minutes)}:${pad(remainingSeconds)}`;
	} else {
		return `${minutes}:${pad(remainingSeconds)}`;
	}
}
