/**
 * 浏览器检测工具
 * 用于自动检测当前浏览器类型，并判断是否为 yt-dlp 支持的 cookie 浏览器
 */

/**
 * yt-dlp 支持的浏览器列表
 * 参考：yt-dlp下载指令参数.md
 */
export const SUPPORTED_BROWSERS = [
    'brave',
    'chrome',
    'chromium',
    'edge', 
    'firefox',
    'opera',
    'safari',
    'vivaldi',
    'whale'
] as const;

export type SupportedBrowser = typeof SUPPORTED_BROWSERS[number];

/**
 * 浏览器检测结果接口
 */
export interface BrowserDetectionResult {
    /** 检测到的浏览器名称 */
    browser: string;
    /** 是否为 yt-dlp 支持的浏览器 */
    isSupported: boolean;
    /** 如果支持，返回对应的 yt-dlp 浏览器参数名 */
    ytDlpBrowserName?: SupportedBrowser;
    /** 浏览器版本（如果可获取） */
    version?: string;
    /** 用户代理字符串 */
    userAgent: string;
}

/**
 * 高级浏览器检测 - 使用多种检测技术
 */
interface AdvancedDetectionResult {
    userAgent: string;
    apiFingerprint: string;
    extensionAPI: string;
    renderingEngine: string;
    jsEngine: string;
    canvasFingerprint: string;
    confidence: number; // 0-100的置信度
}

/**
 * 执行高级浏览器检测
 */
function advancedBrowserDetection(): AdvancedDetectionResult {
    const userAgent = navigator.userAgent;
    
    // API特征检测
    const apiFingerprint = detectAPIFeatures();
    
    // 扩展API检测
    const extensionAPI = detectExtensionAPI();
    
    // 渲染引擎检测
    const renderingEngine = detectRenderingEngine();
    
    // JavaScript引擎检测
    const jsEngine = detectJSEngine();
    
    // Canvas指纹
    const canvasFingerprint = detectCanvasFingerprint();
    
    // 计算检测置信度
    const confidence = calculateConfidence({
        userAgent,
        apiFingerprint,
        extensionAPI,
        renderingEngine,
        jsEngine,
        canvasFingerprint
    });
    
    return {
        userAgent,
        apiFingerprint,
        extensionAPI,
        renderingEngine,
        jsEngine,
        canvasFingerprint,
        confidence
    };
}

/**
 * API特征检测
 */
function detectAPIFeatures(): string {
    const features = [];
    
    // Chrome特有API
    if (typeof (window as any).chrome !== 'undefined') {
        features.push('chrome-api');
        if ((window as any).chrome.webstore) features.push('chrome-webstore');
        if ((window as any).chrome.runtime) features.push('chrome-runtime');
    }
    
    // Firefox特有API
    if (typeof (window as any).InstallTrigger !== 'undefined') {
        features.push('firefox-install-trigger');
    }
    
    // Safari特有API
    if (typeof (window as any).safari !== 'undefined') {
        features.push('safari-api');
    }
    
    // Edge特有API
    if (typeof (window as any).StyleMedia !== 'undefined') {
        features.push('edge-style-media');
    }
    
    // 性能API检测
    if (performance && (performance as any).memory) {
        features.push('performance-memory');
    }
    
    return features.join(',');
}

/**
 * 扩展API检测
 */
function detectExtensionAPI(): string {
    try {
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            if (chrome.runtime.getManifest) {
                return 'chrome-extension-v3';
            }
            return 'chrome-extension';
        }
        
        if (typeof browser !== 'undefined' && browser.runtime) {
            return 'firefox-webextension';
        }
        
        return 'no-extension-api';
    } catch (e) {
        return 'extension-api-error';
    }
}

/**
 * 渲染引擎检测
 */
function detectRenderingEngine(): string {
    // CSS特性检测
    if (typeof CSS !== 'undefined' && CSS.supports) {
        const supports = [];
        
        // 检测特定CSS特性的支持
        if (CSS.supports('display', 'contents')) supports.push('display-contents');
        if (CSS.supports('backdrop-filter', 'blur(1px)')) supports.push('backdrop-filter');
        if (CSS.supports('color', 'color(display-p3 1 0 0)')) supports.push('display-p3');
        
        return supports.join(',') || 'basic-css';
    }
    
    return 'no-css-supports';
}

/**
 * JavaScript引擎检测
 */
function detectJSEngine(): string {
    const features = [];
    
    // V8特征
    if (typeof (Error as any).captureStackTrace === 'function') {
        features.push('v8-stack-trace');
    }
    
    // SpiderMonkey特征
    if (typeof (window as any).uneval === 'function') {
        features.push('spidermonkey-uneval');
    }
    
    // 检测ES特性支持
    try {
        eval('(async () => {})'); // Async/await
        features.push('async-await');
    } catch (e) {}
    
    try {
        eval('class Test {}'); // ES6 classes
        features.push('es6-classes');
    } catch (e) {}
    
    return features.join(',') || 'basic-js';
}

/**
 * Canvas指纹检测
 */
function detectCanvasFingerprint(): string {
    try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) return 'no-canvas';
        
        // 绘制特定的图形和文本
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillStyle = '#f60';
        ctx.fillRect(125, 1, 62, 20);
        ctx.fillStyle = '#069';
        ctx.fillText('🔍 Browser Test', 2, 15);
        ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
        ctx.fillText('Advanced Detection', 4, 45);
        
        // 获取图像数据并生成简单hash
        const imageData = canvas.toDataURL();
        return simpleHash(imageData).slice(0, 8);
    } catch (e) {
        return 'canvas-error';
    }
}

/**
 * 简单hash函数
 */
function simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
}

/**
 * 计算检测置信度
 */
function calculateConfidence(detection: Omit<AdvancedDetectionResult, 'confidence'>): number {
    let score = 0;
    let maxScore = 0;
    
    // User Agent权重: 20分
    maxScore += 20;
    if (detection.userAgent && detection.userAgent.length > 50) {
        score += 20;
    } else if (detection.userAgent) {
        score += 10;
    }
    
    // API特征权重: 30分
    maxScore += 30;
    const apiFeatures = detection.apiFingerprint.split(',').filter(f => f);
    score += Math.min(apiFeatures.length * 6, 30);
    
    // 扩展API权重: 20分
    maxScore += 20;
    if (detection.extensionAPI && detection.extensionAPI !== 'no-extension-api') {
        score += 20;
    }
    
    // 渲染引擎权重: 15分
    maxScore += 15;
    if (detection.renderingEngine && detection.renderingEngine !== 'no-css-supports') {
        score += 15;
    }
    
    // JS引擎权重: 10分
    maxScore += 10;
    if (detection.jsEngine && detection.jsEngine !== 'basic-js') {
        score += 10;
    }
    
    // Canvas指纹权重: 5分
    maxScore += 5;
    if (detection.canvasFingerprint && detection.canvasFingerprint !== 'no-canvas') {
        score += 5;
    }
    
    return Math.round((score / maxScore) * 100);
}

export function detectBrowser(): BrowserDetectionResult {
    const userAgent = navigator.userAgent;
    const result: BrowserDetectionResult = {
        browser: 'unknown',
        isSupported: false,
        userAgent
    };

    // 检测各种浏览器
    // 注意：检测顺序很重要，因为某些浏览器的 UA 包含其他浏览器的标识

    if (userAgent.includes('Whale')) {
        result.browser = 'whale';
        result.isSupported = true;
        result.ytDlpBrowserName = 'whale';
    } else if (userAgent.includes('Vivaldi')) {
        result.browser = 'vivaldi';
        result.isSupported = true;
        result.ytDlpBrowserName = 'vivaldi';
    } else if (userAgent.includes('Brave')) {
        result.browser = 'brave';
        result.isSupported = true;
        result.ytDlpBrowserName = 'brave';
    } else if (userAgent.includes('OPR') || userAgent.includes('Opera')) {
        result.browser = 'opera';
        result.isSupported = true;
        result.ytDlpBrowserName = 'opera';
    } else if (userAgent.includes('Edg')) {
        result.browser = 'edge';
        result.isSupported = true;
        result.ytDlpBrowserName = 'edge';
    } else if (userAgent.includes('Chrome') && !userAgent.includes('Chromium')) {
        // 这里可能是Chrome，也可能是其他伪装成Chrome的浏览器
        result.browser = 'chrome-like';
        result.isSupported = true;
        result.ytDlpBrowserName = 'chrome';
    } else if (userAgent.includes('Chromium')) {
        result.browser = 'chromium';
        result.isSupported = true;
        result.ytDlpBrowserName = 'chromium';
    } else if (userAgent.includes('Firefox')) {
        result.browser = 'firefox';
        result.isSupported = true;
        result.ytDlpBrowserName = 'firefox';
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
        result.browser = 'safari';
        result.isSupported = true;
        result.ytDlpBrowserName = 'safari';
    }

    // 尝试提取版本号
    result.version = extractBrowserVersion(userAgent, result.browser);

    return result;
}

/**
 * 提取浏览器版本号
 * @param userAgent 用户代理字符串
 * @param browserName 浏览器名称
 * @returns 版本号字符串
 */
function extractBrowserVersion(userAgent: string, browserName: string): string | undefined {
    let versionRegex: RegExp;

    switch (browserName) {
        case 'chrome':
        case 'chrome-like':
            versionRegex = /Chrome\/(\d+\.\d+\.\d+\.\d+)/;
            break;
        case 'chromium':
            versionRegex = /Chromium\/(\d+\.\d+\.\d+\.\d+)/;
            break;
        case 'firefox':
            versionRegex = /Firefox\/(\d+\.\d+)/;
            break;
        case 'safari':
            versionRegex = /Version\/(\d+\.\d+)/;
            break;
        case 'edge':
            versionRegex = /Edg\/(\d+\.\d+\.\d+\.\d+)/;
            break;
        case 'vivaldi':
            versionRegex = /Vivaldi\/(\d+\.\d+\.\d+\.\d+)/;
            break;
        case 'brave':
            // Brave 通常使用 Chrome 版本号
            versionRegex = /Chrome\/(\d+\.\d+\.\d+\.\d+)/;
            break;
        case 'opera':
            versionRegex = /OPR\/(\d+\.\d+\.\d+\.\d+)/;
            break;
        case 'whale':
            versionRegex = /Whale\/(\d+\.\d+\.\d+\.\d+)/;
            break;
        default:
            return undefined;
    }

    const match = userAgent.match(versionRegex);
    return match ? match[1] : undefined;
}

/**
 * 获取当前浏览器的 yt-dlp cookie 参数
 * @returns 如果当前浏览器支持，返回 --cookies-from-browser 参数，否则返回 null
 */
export function getYtDlpCookieParam(): string | null {
    const detection = detectBrowser();
    
    if (detection.isSupported && detection.ytDlpBrowserName) {
        return `--cookies-from-browser ${detection.ytDlpBrowserName}`;
    }
    
    return null;
}

/**
 * 检查指定浏览器是否被 yt-dlp 支持
 * @param browserName 浏览器名称
 * @returns 是否支持
 */
export function isBrowserSupported(browserName: string): boolean {
    return SUPPORTED_BROWSERS.includes(browserName.toLowerCase() as SupportedBrowser);
}

/**
 * 获取所有支持的浏览器选项（用于手动选择）
 * @returns 浏览器选项数组
 */
export function getSupportedBrowserOptions(): Array<{value: SupportedBrowser, label: string}> {
    return SUPPORTED_BROWSERS.map(browser => ({
        value: browser,
        label: browser.charAt(0).toUpperCase() + browser.slice(1)
    }));
}

/**
 * 生成浏览器检测报告（用于调试）
 * @returns 检测报告字符串
 */
export function generateDetectionReport(): string {
    const detection = detectBrowser();

    return `
浏览器检测报告:
- 检测到的浏览器: ${detection.browser}
- 版本: ${detection.version || '未知'}
- yt-dlp 支持: ${detection.isSupported ? '是' : '否'}
- yt-dlp 参数名: ${detection.ytDlpBrowserName || '不适用'}
- User Agent: ${detection.userAgent}
    `.trim();
}

/**
 * 调试函数：在控制台输出详细的浏览器检测信息
 */
export function debugBrowserDetection(): void {
    const userAgent = navigator.userAgent;
    const basicDetection = detectBrowser();
    const advancedDetection = advancedBrowserDetection();

    console.group('🔍 浏览器检测调试信息');
    console.log('User Agent:', userAgent);
    console.log('基础检测结果:', basicDetection);
    console.log('高级检测结果:', advancedDetection);

    // 检查各种可能的浏览器标识符
    const checks = {
        'Whale': userAgent.includes('Whale'),
        'Vivaldi': userAgent.includes('Vivaldi'),
        'Brave': userAgent.includes('Brave'),
        'OPR': userAgent.includes('OPR'),
        'Opera': userAgent.includes('Opera'),
        'Edg': userAgent.includes('Edg'),
        'Chrome': userAgent.includes('Chrome'),
        'Chromium': userAgent.includes('Chromium'),
        'Firefox': userAgent.includes('Firefox'),
        'Safari': userAgent.includes('Safari')
    };

    console.log('标识符检查:', checks);
    
    // 显示API特征
    console.log('API特征:', advancedDetection.apiFingerprint);
    console.log('扩展API:', advancedDetection.extensionAPI);
    console.log('检测置信度:', advancedDetection.confidence + '%');
    
    console.groupEnd();
}

/**
 * 浏览器特性检测
 * 检测浏览器是否支持特定的 Web API
 */
export interface BrowserCapabilities {
    /** 是否支持 Chrome 扩展 API */
    supportsExtensions: boolean;
    /** 是否支持 Service Worker */
    supportsServiceWorker: boolean;
    /** 是否支持 Web Components */
    supportsWebComponents: boolean;
    /** 是否支持 ES6 模块 */
    supportsES6Modules: boolean;
}

/**
 * 检测浏览器能力
 * @returns 浏览器能力对象
 */
export function detectBrowserCapabilities(): BrowserCapabilities {
    return {
        supportsExtensions: typeof chrome !== 'undefined' && !!chrome.runtime,
        supportsServiceWorker: 'serviceWorker' in navigator,
        supportsWebComponents: 'customElements' in window,
        supportsES6Modules: 'noModule' in HTMLScriptElement.prototype
    };
}
