<script lang="ts">
	export let bgColor: string = "bg-secondary";
	export let width: string = "w-[24px]";
	export let onClick: (() => void) | undefined = undefined;
	export let hide = false;
	export let title = "";
	let className = "";
	export { className as class };
</script>

<button {title} on:click={onClick} class={`neu-btn ${width} ${className} ${hide ? "invisible" : "visible"}`}>
	<slot />
</button>

<style>
	:global(.neu-btn) {
		height: 1.5rem;
		border-radius: 0.375rem;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 0.75rem;
		color: #4b5563;
		fill: #4b5563;
		background: linear-gradient(to bottom right, #e5e7eb, #d1d5db);
		transition: all 0.2s ease;
		box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.1), -3px -3px 6px rgba(255, 255, 255, 0.8);
		border: none;
	}

	:global(.neu-btn:hover) {
		transform: translateY(-1px);
		box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.15), -4px -4px 8px rgba(255, 255, 255, 0.9);
	}

	:global(.neu-btn:active) {
		transform: translateY(0);
		box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.2), inset -2px -2px 4px rgba(255, 255, 255, 0.8);
	}

	/* 深色模式 */
	:global(.dark) :global(.neu-btn) {
		color: #d1d5db;
		fill: #d1d5db;
		background: linear-gradient(to bottom right, #4b5563, #374151);
		box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3), -3px -3px 6px rgba(255, 255, 255, 0.05);
	}

	:global(.dark) :global(.neu-btn:hover) {
		box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.4), -4px -4px 8px rgba(255, 255, 255, 0.08);
	}

	:global(.dark) :global(.neu-btn:active) {
		box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.3), inset -2px -2px 4px rgba(255, 255, 255, 0.1);
	}
</style>
