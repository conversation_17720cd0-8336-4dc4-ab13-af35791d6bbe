<script lang="ts">
	export let onClick: (() => void) | undefined = undefined;
	export let hide = false;
</script>

<button
	type="button"
	on:click={onClick}
	class={`bg-gradient-to-br from-red-400 to-red-600 hover:from-red-500 hover:to-red-700
			 rounded-full p-2.5 inline-flex items-center justify-center text-white
			 focus:outline-none focus:ring-2 focus:ring-red-500 shadow-md hover:shadow-lg
			 transition-all duration-200 transform hover:-translate-y-0.5
			 active:shadow-inner active:transform-none ${hide ? 'invisible' : ''}`}
>
	<svg class="h-3.5 w-3.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
		<path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M6 18L18 6M6 6l12 12" />
	</svg>
</button>
