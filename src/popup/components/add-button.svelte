<script lang="ts">
	export let onClick: (() => void) | undefined = undefined;
</script>

<button class="w-full h-auto" on:click={onClick}>
	<div class="flex-1 h-full">
		<div class="flex items-center justify-center flex-1 h-full p-3
					bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800
					shadow-md hover:shadow-lg rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-600
					hover:border-gray-400 dark:hover:border-gray-500 transition-all duration-200
					transform hover:-translate-y-0.5 active:shadow-inner active:transform-none">
			<div class="relative">
				<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
				</svg>
			</div>
		</div>
	</div>
</button>
