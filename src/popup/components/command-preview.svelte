<script lang="ts">
	import type { IVideo } from '../../interfaces/video';
	import type { IVideoClip } from '../../interfaces/clip-time';
	import { generateSingleClipCommand, validateCommand, getBrowserDetectionInfo, getYtDlpCookieParam, type CommandOptions } from '../../utils/yt-dlp-generator';
	import { getSupportedBrowserOptions, type SupportedBrowser, debugBrowserDetection } from '../../utils/browser-detector';
	import { storage } from '../stores/storage';
	import { storageDriver } from '../../storage-driver';
	
	export let video: IVideo;
	export let clip: IVideoClip | null = null; // 如果为null则生成批量命令
	export let isOpen: boolean = false;

	let command: string = '';
	let displayCommand: string = '';
	let copySuccess: boolean = false;
	let commandOptions: CommandOptions = {
		includeMetadata: false,
		includeSubtitles: false,
		writeAutoSubs: false,
		includeCookies: false,
		delogoRegions: clip?.delogoRegions || video.delogoRegions || [],
		videoHeight: video.videoHeight
	};

	// 获取浏览器检测信息和支持的浏览器选项
	let browserInfo = getBrowserDetectionInfo($storage);
	let supportedBrowsers = getSupportedBrowserOptions();
	let selectedBrowser: SupportedBrowser | '' = '';
	let showBrowserSelector: boolean = false;

	// 响应式更新commandOptions中的videoHeight
	$: if (video.videoHeight !== commandOptions.videoHeight) {
		commandOptions.videoHeight = video.videoHeight;
	}

	// 当模态框打开时，根据用户设置初始化cookie选项
	$: if (isOpen && $storage.enableCookiesByDefault !== undefined) {
		commandOptions.includeCookies = $storage.enableCookiesByDefault;
		selectedBrowser = $storage.preferredBrowser || '';
		browserInfo = getBrowserDetectionInfo($storage);
	}

	
	
	
	
	
	

	
	let isSelectingFolder = false;
	
	$: if (isOpen) {
		generateCommand();
	}
	
	function generateCommand() {
		try {
			const { includeMetadata, includeSubtitles, writeAutoSubs, includeCookies, delogoRegions, videoHeight } = commandOptions;

			const optionsToPass: CommandOptions = {
				includeMetadata,
				includeSubtitles,
				writeAutoSubs,
				includeCookies,
				manualBrowser: selectedBrowser || null,
				delogoRegions: delogoRegions || video.delogoRegions || [],
				videoHeight: videoHeight || video.videoHeight
			};

			if (clip) {
				// 生成单个片段命令
				command = generateSingleClipCommand(video.id, clip, optionsToPass, $storage);
			} else {
				// 生成批量复制指令（为每个片段生成单独的命令）
				const commands = video.clips.map(clipItem =>
					generateSingleClipCommand(video.id, clipItem, optionsToPass, $storage)
				);
				command = commands.join('\n\n');
			}

			// 生成分行显示格式
			if (clip) {
				// 单个片段命令
				displayCommand = formatCommandForDisplay(command);
			} else {
				// 批量命令：每个命令分别格式化
				const commands = command.split('\n\n');
				const formattedCommands = commands.map(cmd => formatCommandForDisplay(cmd));
				displayCommand = formattedCommands.join('\n\n');
			}
		} catch (error) {
			command = `错误: ${error.message}`;
			displayCommand = command;
		}
	}

	
	
	async function copyCommand() {
		try {
			await navigator.clipboard.writeText(command);
			copySuccess = true;
			setTimeout(() => {
				copySuccess = false;
			}, 2000);
		} catch (error) {
			console.error('复制失败:', error);
		}
	}
	
	function closeModal() {
		isOpen = false;
	}

	// 保存用户选择的浏览器到设置
	async function saveBrowserPreference(browser: string) {
		if (browser && browser.trim()) {
			storage.update((prev) => {
				prev.preferredBrowser = browser.trim();
				prev.lastSync = new Date().getTime();
				return prev;
			});
			await storageDriver.set($storage);

			// 更新浏览器信息
			browserInfo = getBrowserDetectionInfo($storage);
			selectedBrowser = browser as SupportedBrowser;
			showBrowserSelector = false;

			// 重新生成命令
			generateCommand();
		}
	}

	// 切换浏览器选择器显示状态
	function toggleBrowserSelector() {
		showBrowserSelector = !showBrowserSelector;
	}

	// 重置浏览器选择（使用自动检测）
	async function resetBrowserSelection() {
		storage.update((prev) => {
			prev.preferredBrowser = '';
			prev.lastSync = new Date().getTime();
			return prev;
		});
		await storageDriver.set($storage);

		browserInfo = getBrowserDetectionInfo($storage);
		selectedBrowser = '';
		showBrowserSelector = false;
		generateCommand();
	}

	/**
	 * 格式化命令为多行显示格式
	 */
	function formatCommandForDisplay(singleLineCommand: string): string {
		if (!singleLineCommand) return '';

		// 将命令按空格分割，但保持引号内的内容完整
		const parts = [];
		let current = '';
		let inQuotes = false;
		let quoteChar = '';

		for (let i = 0; i < singleLineCommand.length; i++) {
			const char = singleLineCommand[i];

			if ((char === '"' || char === "'") && !inQuotes) {
				inQuotes = true;
				quoteChar = char;
				current += char;
			} else if (char === quoteChar && inQuotes) {
				inQuotes = false;
				quoteChar = '';
				current += char;
			} else if (char === ' ' && !inQuotes) {
				if (current.trim()) {
					parts.push(current.trim());
					current = '';
				}
			} else {
				current += char;
			}
		}

		if (current.trim()) {
			parts.push(current.trim());
		}

		// 按逻辑分组格式化
		const lines = [];
		let i = 0;

		while (i < parts.length) {
			const part = parts[i];

			if (part === 'yt-dlp') {
				// 第一行：yt-dlp + URL
				lines.push(`${part} ${parts[i + 1] || ''}`);
				i += 2;
			} else if (part === '--cookies-from-browser') {
				// 浏览器cookie参数
				lines.push(`${part} ${parts[i + 1] || ''}`);
				i += 2;
			} else if (part === '-f') {
				// 格式选择参数
				lines.push(`${part} ${parts[i + 1] || ''}`);
				i += 2;
			} else if (part === '-S') {
				// 排序参数
				lines.push(`${part} ${parts[i + 1] || ''}`);
				i += 2;
			} else if (part === '--download-sections') {
				// 片段下载参数
				lines.push(`${part} ${parts[i + 1] || ''}`);
				i += 2;
			} else if (part === '--recode-video') {
				// 重编码参数
				lines.push(`${part} ${parts[i + 1] || ''}`);
				i += 2;
			} else if (part === '--ppa') {
				// FFmpeg后处理参数
				lines.push(`${part} ${parts[i + 1] || ''}`);
				i += 2;
			} else if (part === '-P') {
				// 下载路径参数
				lines.push(`${part} ${parts[i + 1] || ''}`);
				i += 2;
			} else if (part === '--output') {
				// 输出模板参数
				lines.push(`${part} ${parts[i + 1] || ''}`);
				i += 2;
			} else if (part.startsWith('--')) {
				// 其他单独的参数
				if (i + 1 < parts.length && !parts[i + 1].startsWith('-')) {
					lines.push(`${part} ${parts[i + 1]}`);
					i += 2;
				} else {
					lines.push(part);
					i += 1;
				}
			} else {
				// 其他参数
				lines.push(part);
				i += 1;
			}
		}

		return lines.join('\n');
	}

	// 验证命令
	$: validation = validateCommand(command);
</script>

{#if isOpen}
	<!-- 模态框背景 -->
	<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" on:click={closeModal}>
		<!-- 模态框内容 -->
		<div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full mx-4 overflow-y-auto" on:click|stopPropagation>
			<!-- 标题 -->
			<div class="flex items-center justify-between mb-4">
				<h3 class="text-lg font-semibold text-gray-900 dark:text-white">
					{clip ? '单片段' : '批量复制'}yt-dlp命令生成
				</h3>
				<button
					on:click={closeModal}
					class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
				>
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
					</svg>
				</button>
			</div>
			
			<!-- 视频信息 -->
			<div class="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded">
				<h4 class="font-medium text-gray-900 dark:text-white mb-1">{video.title}</h4>
				<p class="text-sm text-gray-600 dark:text-gray-400">
					视频ID: {video.id} |
					{clip ? '单个片段' : `${video.clips.length} 个独立命令`}
				</p>
			</div>
			
			<!-- 命令选项 -->
			<div class="mb-4 space-y-3">
				<h4 class="font-medium text-gray-900 dark:text-white">命令选项</h4>
				


				

				<!-- 选项开关 -->
				<div class="flex flex-wrap gap-4">
					<label class="flex items-center">
						<input
							type="checkbox"
							bind:checked={commandOptions.includeMetadata}
							on:change={generateCommand}
							class="mr-2"
						>
						<span class="text-sm text-gray-700 dark:text-gray-300">嵌入元数据</span>
					</label>

					<label class="flex items-center">
						<input
							type="checkbox"
							bind:checked={commandOptions.includeSubtitles}
							on:change={generateCommand}
							class="mr-2"
						>
						<span class="text-sm text-gray-700 dark:text-gray-300">下载字幕</span>
					</label>

					<label class="flex items-center">
						<input
							type="checkbox"
							bind:checked={commandOptions.includeCookies}
							on:change={generateCommand}
							class="mr-2"
						>
						<span class="text-sm text-gray-700 dark:text-gray-300">使用浏览器Cookie</span>
					</label>

					{#if commandOptions.includeSubtitles}
						<label class="flex items-center">
							<input
								type="checkbox"
								bind:checked={commandOptions.writeAutoSubs}
								on:change={generateCommand}
								class="mr-2"
							>
							<span class="text-sm text-gray-700 dark:text-gray-300">下载自动字幕</span>
						</label>
					{/if}
				</div>

				<!-- 元数据注意提示 -->
				{#if commandOptions.includeMetadata}
					<div class="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-md">
						<div class="text-sm text-yellow-800 dark:text-yellow-200">
							<strong>注意:</strong> 可能出现"Function not implemented"错误
						</div>
					</div>
				{/if}

				<!-- Cookie和浏览器选择 -->
				{#if commandOptions.includeCookies}
					<div class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
						<div class="text-sm text-blue-800 dark:text-blue-200">
							<div class="flex items-center justify-between mb-2">
								<strong>浏览器检测:</strong>
								<div class="flex gap-2">
									<button
										on:click={debugBrowserDetection}
										class="text-xs text-gray-600 dark:text-gray-400 hover:underline"
										title="在控制台查看详细检测信息"
									>
										🔍 调试
									</button>
									<button
										on:click={toggleBrowserSelector}
										class="text-xs text-blue-600 dark:text-blue-400 hover:underline"
									>
										{showBrowserSelector ? '隐藏选择' : '手动选择'}
									</button>
								</div>
							</div>

							<div class="mb-2">
								检测到: {browserInfo.browser}
								{#if browserInfo.browser === 'chrome-like'}
									<span class="text-orange-600 dark:text-orange-400">⚠️ 可能不是真正的Chrome</span>
								{:else if browserInfo.isSupported}
									<span class="text-green-600 dark:text-green-400">✓ 支持</span>
									{#if browserInfo.ytDlpBrowserName && !selectedBrowser}
										- 将使用 {browserInfo.ytDlpBrowserName} cookie
									{/if}
								{:else}
									<span class="text-red-600 dark:text-red-400">✗ 不支持</span>
								{/if}

								{#if browserInfo.isUserPreferred}
									<span class="text-purple-600 dark:text-purple-400">(用户设置)</span>
								{/if}
							</div>

							{#if browserInfo.browser === 'chrome-like' && !browserInfo.isUserPreferred}
								<div class="mb-2 text-xs text-orange-700 dark:text-orange-300 bg-orange-50 dark:bg-orange-900/20 p-2 rounded">
									检测到基于Chromium的浏览器（如ARC、Sidekick等）。为确保最佳兼容性，建议手动选择具体的浏览器类型。
								</div>
							{/if}

							<!-- 浏览器选择器 -->
							{#if showBrowserSelector || (!browserInfo.isSupported && !browserInfo.isUserPreferred) || (browserInfo.browser === 'chrome-like' && !browserInfo.isUserPreferred)}
								<div class="mt-3 p-3 bg-white dark:bg-gray-800 rounded border">
									<label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
										选择浏览器 (将保存为偏好设置):
									</label>
									<div class="grid grid-cols-2 gap-2">
										{#each supportedBrowsers as browser}
											<button
												on:click={() => saveBrowserPreference(browser.value)}
												class="px-3 py-2 text-xs rounded border border-gray-300 dark:border-gray-600
													   hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors
													   {selectedBrowser === browser.value ? 'bg-blue-100 dark:bg-blue-900 border-blue-500' : ''}"
											>
												{browser.label}
											</button>
										{/each}
									</div>

									{#if browserInfo.isUserPreferred}
										<button
											on:click={resetBrowserSelection}
											class="mt-2 text-xs text-gray-600 dark:text-gray-400 hover:underline"
										>
											重置为自动检测
										</button>
									{/if}
								</div>
							{/if}

							{#if !browserInfo.isSupported && !browserInfo.isUserPreferred}
								<div class="mt-2 text-xs text-orange-700 dark:text-orange-300">
									当前浏览器不受yt-dlp支持，请手动选择已安装的浏览器
								</div>
							{/if}
						</div>
					</div>
				{/if}
			</div>

			<!-- Delogo 区域信息 -->
			{#if commandOptions.delogoRegions && commandOptions.delogoRegions.length > 0}
				<div class="mb-4">
					<div>
						<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
							Delogo 区域
						</label>
						<div class="text-xs text-gray-500 dark:text-gray-400">
							{#if video.delogoRegions && video.delogoRegions.length > 0}
								已配置 {video.delogoRegions.filter(r => r.enabled).length}/{video.delogoRegions.length} 个去logo区域
							{:else}
								未配置去logo区域
							{/if}
						</div>
					</div>
				</div>
			{/if}
			
			<!-- 命令预览 -->
			<div class="mb-4">
				<div class="flex items-center justify-between mb-2">
					<h4 class="font-medium text-gray-900 dark:text-white">生成的命令</h4>
					<button
						on:click={copyCommand}
						class="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm font-medium transition-colors"
						class:bg-green-500={copySuccess}
						class:hover:bg-green-600={copySuccess}
					>
						{copySuccess ? '✓ 已复制' : '📋 复制命令'}
					</button>
				</div>
				
				<div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-x-auto">
					<pre>{displayCommand}</pre>
				</div>
			</div>
			
			<!-- 验证结果 -->
			{#if validation.errors.length > 0 || validation.warnings.length > 0}
				<div class="mb-4">
					{#if validation.errors.length > 0}
						<div class="mb-2">
							<h5 class="text-sm font-medium text-red-600 dark:text-red-400 mb-1">错误:</h5>
							<ul class="text-sm text-red-600 dark:text-red-400 list-disc list-inside">
								{#each validation.errors as error}
									<li>{error}</li>
								{/each}
							</ul>
						</div>
					{/if}
					
					{#if validation.warnings.length > 0}
						<div>
							<h5 class="text-sm font-medium text-yellow-600 dark:text-yellow-400 mb-1">警告:</h5>
							<ul class="text-sm text-yellow-600 dark:text-yellow-400 list-disc list-inside">
								{#each validation.warnings as warning}
									<li>{warning}</li>
								{/each}
							</ul>
						</div>
					{/if}
				</div>
			{/if}
			
			<!-- 安装指南 -->
			<div class="text-sm text-gray-600 dark:text-gray-400">
				<div class="p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
					<h6 class="font-medium text-blue-800 dark:text-blue-200 mb-1">首次使用安装指南:</h6>
					<div class="text-xs space-y-1">
						<p><strong>Windows:</strong></p>
						<code class="bg-gray-100 dark:bg-gray-800 px-1 rounded">winget install yt-dlp</code><br>
						<code class="bg-gray-100 dark:bg-gray-800 px-1 rounded">winget install FFmpeg</code>

						<p class="mt-2"><strong>Mac:</strong></p>
						<code class="bg-gray-100 dark:bg-gray-800 px-1 rounded">brew install yt-dlp ffmpeg</code><br>
						<span class="text-xs text-gray-500">（需要先安装 Homebrew: <code class="bg-gray-100 dark:bg-gray-800 px-1 rounded">/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"</code>）</span>
					</div>
				</div>
			</div>
		</div>
	</div>
{/if}
