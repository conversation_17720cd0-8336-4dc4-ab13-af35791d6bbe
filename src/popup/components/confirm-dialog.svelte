<script lang="ts">
	export let isOpen = false;
	export let title = '确认操作';
	export let message = '';
	export let confirmText = '确认';
	export let cancelText = '取消';
	export let onConfirm: () => void = () => {};
	export let onCancel: () => void = () => {};

	function handleConfirm() {
		isOpen = false;
		onConfirm();
	}

	function handleCancel() {
		isOpen = false;
		onCancel();
	}

	function handleBackdropClick(event: MouseEvent) {
		if (event.target === event.currentTarget) {
			handleCancel();
		}
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			handleCancel();
		} else if (event.key === 'Enter') {
			handleConfirm();
		}
	}
</script>

<svelte:window on:keydown={handleKeydown} />

{#if isOpen}
	<div 
		class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] backdrop-blur-sm"
		on:click={handleBackdropClick}
		role="dialog"
		aria-modal="true"
		aria-labelledby="dialog-title"
		aria-describedby="dialog-message"
	>
		<div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden transform transition-all duration-200 scale-100">
			<div class="p-6">
				<div class="flex items-center gap-3 mb-4">
					<div class="w-8 h-8 bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center flex-shrink-0">
						<svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
						</svg>
					</div>
					<h3 id="dialog-title" class="text-lg font-semibold text-gray-900 dark:text-white">
						{title}
					</h3>
				</div>
				
				<p id="dialog-message" class="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
					{message}
				</p>
				
				<div class="flex gap-3">
					<button
						type="button"
						class="flex-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-4 py-3 rounded-xl font-medium transition-all duration-200 border border-gray-200 dark:border-gray-600"
						on:click={handleCancel}
					>
						{cancelText}
					</button>
					<button
						type="button"
						class="flex-1 bg-gradient-to-br from-red-500 to-red-700 hover:from-red-600 hover:to-red-800 text-white px-4 py-3 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 active:shadow-inner active:transform-none"
						on:click={handleConfirm}
					>
						{confirmText}
					</button>
				</div>
			</div>
		</div>
	</div>
{/if}