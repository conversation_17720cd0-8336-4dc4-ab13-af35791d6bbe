<script lang="ts">
	import { onMount } from 'svelte';
	import Clipper from '../components/clipper.svelte';
	import { storageDriver } from '../../storage-driver';
	import { storage } from '../stores/storage';

	let youtubeTab: chrome.tabs.Tab | undefined;
	let videoId: string | undefined | null;

	let init = false;

	$: $storage,
		(async () => {
			if (!init) {
				return;
			}
			await storageDriver.set($storage);
		})();

	onMount(async () => {
		const res = await storageDriver.get();
		init = true;
		storage.set(res);

		const activeTab = (await chrome.tabs.query({ active: true, currentWindow: true }))[0];
		if (activeTab.url?.indexOf('youtube.com/watch') === -1) {
			return;
		}
		const regex = /[?&]v=([^&#]+)/;
		const match = activeTab.url?.match(regex);
		videoId = match && match[1];
		if (!videoId) {
			return;
		}
		youtubeTab = activeTab;
	});


</script>

<div class="w-full flex flex-col items-center">
	{#if youtubeTab && videoId}
		<Clipper tab={youtubeTab} id={videoId} />
	{/if}
</div>
