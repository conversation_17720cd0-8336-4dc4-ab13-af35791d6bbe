@tailwind base;
@tailwind components;
@tailwind utilities;

/* 侧边栏特定样式 */
body {
  margin: 0;
  padding: 0;
  width: 100vw;
  min-width: 320px;
  min-height: 100vh;
  overflow-x: hidden;
  background-color: #f8fafc; /* 浅色模式背景 */
}

/* 深色模式背景 */
body.dark {
  background-color: #1a202c; /* 深色模式背景 */
}

/* 确保侧边栏容器响应式宽度 */
#app {
  width: 100%;
  min-width: 320px;
  min-height: 100vh;
  background-color: inherit;
}

/* 中等屏幕及以上：固定550px宽度 */
@media (min-width: 768px) {
  body {
    width: 550px;
  }

  #app {
    width: 550px;
    min-width: 550px;
    max-width: 550px;
  }
}

/* 侧边栏模式下的滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 深色模式下的滚动条 */
.dark ::-webkit-scrollbar-track {
  background: #2d3748;
}

.dark ::-webkit-scrollbar-thumb {
  background: #4a5568;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #718096;
}
