import { storageDriver, type IStorage } from '../storage-driver';

console.log('================= YT CLIPPER =================');

// Content script现在支持侧边栏注入功能

let prevId = '';
let storage: IStorage | undefined
let controller: AbortController | undefined
let adsObserver: MutationObserver | undefined
let prevPlayerType: 'video' | 'playlist' | undefined

// 侧边栏相关变量
let sidebarContainer: HTMLElement | null = null;
let sidebarVisible = false;
let sidebarApp: any = null;

async function main() {
	if (window.location.href.indexOf('youtube.com') === -1) {
		return;
	}
	storage = await storageDriver.get();
	if (!storage) {
		return
	}

	// 初始化侧边栏系统
	initSidebar();

	// 监听来自background script的消息
	chrome.runtime.onMessage.addListener(handleMessage);

	const observer = new MutationObserver(observerCallback);
	observer.observe(document.body, { attributes: false, childList: true, subtree: false });
}
main();


async function observerCallback() {
	// autoSkipAdHandler()
	if (window.location.href.indexOf('youtube.com/watch') === -1) {
		return;
	}
	const regex = /[?&]v=([^&#]+)/;
	const match = window.location.href.match(regex);

	const videoId = match && match[1];
	if (!videoId) {
		return;
	}
	if (videoId === prevId) {
		return;
	}
	prevId = videoId;
	controller?.abort();

	console.log(`============ PROCESS ${videoId} ============`);
	executeVideo(videoId);
}

function getRandomNumber(min: number, max: number) {
	return Math.floor(Math.random() * (max - min + 1)) + min;
}

async function executeVideo(videoId: string) {
	const isPlaylist = window.location.href.indexOf('list=') !== -1;
	if (isPlaylist && prevPlayerType !== 'playlist' && storage!.alwaysShuffle) {
		setTimeout(() => {
			console.log('PERFORM ALWAYS SHUFFLE');
			const shuffleButton = document.querySelector('[aria-label="Shuffle playlist"]') as HTMLButtonElement;
			const isShuffled = shuffleButton?.getAttribute('aria-pressed') === 'true';
			if (!isShuffled) {
				shuffleButton?.click();
			}
		}, 2000);
	}
	prevPlayerType = isPlaylist ? 'playlist' : 'video'


	const video = storage!.videos[videoId];
	if (!video) {
		return;
	}

	// Select the video element on the YouTube page
	let videoElement = document.querySelector('video');
	if (videoElement == null) {
		return;
	}

	let clipPosition = 0;
	if (video.clips[clipPosition].start >= 0) {
		videoElement.currentTime = video.clips[clipPosition].start;
	}
	if (video.clips[clipPosition].start < 0) {
		video.clips[clipPosition].start = 0;
	}
	if (video.clips[clipPosition].end < 0) {
		return;
	}

	const listener = () => {
		const curr = videoElement!.currentTime;
		if (curr < video.clips[clipPosition].end) {
			return;
		}
		clipPosition++;

		if (clipPosition < video.clips.length) {
			videoElement!.currentTime = video.clips[clipPosition].start;
			return;
		}

		if (isPlaylist) {
			controller?.abort();
			controller = undefined;

			const playlist = document.querySelectorAll('#playlist-items');
			if (playlist.length === 0) {
				console.log('WHY 0???');
				return;
			}

			const shuffleButton = document.querySelector('[aria-label="Shuffle playlist"]') as HTMLButtonElement;
			const isShuffled = shuffleButton?.getAttribute('aria-pressed') === 'true';
			if (isShuffled) {
				const nextIdx = getRandomNumber(0, playlist.length - 1);
				const a = playlist.item(nextIdx).children[0] as any;
				a.click();
				return;
			}

			const idx = parseInt(new URLSearchParams(window.location.href).get('index') ?? '') ?? 1;
			let nextIdx = idx + 1;
			if (nextIdx >= playlist.length) {
				nextIdx = 1;
			}
			(playlist.item(nextIdx - 1).children[0] as any).click();
			return;
		}



		const keyEvent = new KeyboardEvent('keydown', {
			key: 'k',
			keyCode: 75,
			code: 'KeyK',
			which: 75,
			shiftKey: false,
			ctrlKey: false,
			metaKey: false,
		});
		document.dispatchEvent(keyEvent);
		clipPosition = 0;
		controller?.abort();
		controller = undefined;
	};
	videoElement.addEventListener('timeupdate', listener);

	controller = new AbortController();
	controller.signal.onabort = () => {
		console.log('============ ABORT ============');
		videoElement!.removeEventListener('timeupdate', listener);
	};
}

function autoSkipAdHandler() {
	if (!storage?.autoSkipAd || adsObserver) {
		return
	}
	const adsContainer: HTMLElement | null = document.querySelector('.video-ads');
	if (!adsContainer) {
		return;
	}
	console.log('PERFORM AUTO SKIP');
	const mutationCallback = function (mutations: MutationRecord[], observer: MutationObserver) {
		if (mutations.length === 0) {
			return;
		}
		if (mutations[0].addedNodes.length === 0) {
			return;
		}
		console.log('NEW ADS');
		setTimeout(() => {
			console.log('TRYING TO SKIP');
			const button: HTMLButtonElement | null = document.querySelector('.ytp-ad-skip-button');
			if (!button) {
				console.log('NO SKIP BUTTON');
				return;
			}
			console.log('PERFOM SKIP ADS');
			button.click();
		}, 1000);
	};
	adsObserver = new MutationObserver(mutationCallback);
	adsObserver.observe(adsContainer, { childList: true });
}

// ==================== 侧边栏功能 ====================

// 处理来自background script的消息
function handleMessage(message: any, sender: any, sendResponse: any) {
	switch (message.type) {
		case 'TOGGLE_SIDEBAR':
			toggleSidebar();
			break;
		case 'SHOW_SIDEBAR':
			showSidebar();
			break;
		case 'HIDE_SIDEBAR':
			hideSidebar();
			break;
	}
}

// 初始化侧边栏系统
function initSidebar() {
	// 检查用户偏好，如果启用了侧边栏则创建容器
	if (storage?.preferSidebar) {
		createSidebarContainer();
	}
}

// 创建侧边栏容器
function createSidebarContainer() {
	if (sidebarContainer) {
		return; // 已存在
	}

	// 注入样式
	injectSidebarStyles();

	// 创建侧边栏容器
	sidebarContainer = document.createElement('div');
	sidebarContainer.id = 'yt-clipper-sidebar';

	// 添加到页面
	document.body.appendChild(sidebarContainer);

	// 创建关闭按钮
	const closeButton = document.createElement('button');
	closeButton.className = 'yt-clipper-close-btn';
	closeButton.innerHTML = '×';
	closeButton.title = '关闭侧边栏';
	closeButton.onclick = hideSidebar;
	sidebarContainer.appendChild(closeButton);

	// 创建内部应用容器
	const appContainer = document.createElement('div');
	appContainer.id = 'yt-clipper-sidebar-app';
	sidebarContainer.appendChild(appContainer);

	console.log('侧边栏容器已创建');
}

// 注入侧边栏样式
function injectSidebarStyles() {
	// 检查是否已注入
	if (document.getElementById('yt-clipper-sidebar-styles')) {
		return;
	}

	// 创建样式元素
	const styleElement = document.createElement('style');
	styleElement.id = 'yt-clipper-sidebar-styles';

	// 这里我们需要将CSS内容内联，或者通过其他方式加载
	// 暂时使用基础样式，后续可以优化
	styleElement.textContent = `
		#yt-clipper-sidebar {
			all: initial;
			position: fixed !important;
			top: 0 !important;
			right: -550px !important;
			width: 550px !important;
			height: 100vh !important;
			background: #f8fafc !important;
			border-left: 1px solid #e2e8f0 !important;
			box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15) !important;
			z-index: 999999 !important;
			transition: right 0.3s ease-in-out !important;
			overflow-y: auto !important;
			overflow-x: hidden !important;
			font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
			font-size: 14px !important;
			line-height: 1.5 !important;
			color: #1a202c !important;
		}

		#yt-clipper-sidebar.visible {
			right: 0 !important;
		}

		#yt-clipper-sidebar-app {
			width: 100% !important;
			height: 100% !important;
			padding: 0 !important;
			margin: 0 !important;
		}

		.yt-clipper-close-btn {
			position: absolute !important;
			top: 10px !important;
			right: 10px !important;
			width: 32px !important;
			height: 32px !important;
			border: none !important;
			background: rgba(0, 0, 0, 0.1) !important;
			border-radius: 50% !important;
			cursor: pointer !important;
			display: flex !important;
			align-items: center !important;
			justify-content: center !important;
			font-size: 18px !important;
			color: #64748b !important;
			transition: all 0.2s ease !important;
			z-index: 1000000 !important;
		}

		.yt-clipper-close-btn:hover {
			background: rgba(0, 0, 0, 0.2) !important;
			color: #374151 !important;
		}

		@media (max-width: 1200px) {
			#yt-clipper-sidebar {
				width: 100vw !important;
				right: -100vw !important;
			}

			#yt-clipper-sidebar.visible {
				right: 0 !important;
			}
		}
	`;

	// 添加到head
	document.head.appendChild(styleElement);
}

// 切换侧边栏显示/隐藏
function toggleSidebar() {
	if (sidebarVisible) {
		hideSidebar();
	} else {
		showSidebar();
	}
}

// 显示侧边栏
async function showSidebar() {
	if (!sidebarContainer) {
		createSidebarContainer();
	}

	if (!sidebarContainer) {
		console.error('无法创建侧边栏容器');
		return;
	}

	// 显示侧边栏
	sidebarContainer.classList.add('visible');
	sidebarVisible = true;

	// 如果还没有加载应用，则加载
	if (!sidebarApp) {
		await loadSidebarApp();
	}

	console.log('侧边栏已显示');
}

// 隐藏侧边栏
function hideSidebar() {
	if (sidebarContainer) {
		sidebarContainer.classList.remove('visible');
		sidebarVisible = false;
		console.log('侧边栏已隐藏');
	}
}

// 加载侧边栏应用
async function loadSidebarApp() {
	const appContainer = document.getElementById('yt-clipper-sidebar-app');
	if (!appContainer) {
		console.error('找不到应用容器');
		return;
	}

	// 显示加载中
	appContainer.innerHTML = `
		<div style="padding: 20px; text-align: center; color: #64748b;">
			<div style="margin-bottom: 10px;">🔄</div>
			<div>正在加载 YouTube Clipper...</div>
		</div>
	`;

	try {
		// 动态导入侧边栏应用入口
		const { initSidebarApp } = await import('./sidebar-entry');

		// 清空加载提示
		appContainer.innerHTML = '';

		// 初始化Svelte应用
		sidebarApp = initSidebarApp(appContainer);

		if (sidebarApp) {
			console.log('侧边栏Svelte应用加载成功');
		} else {
			throw new Error('应用初始化失败');
		}
	} catch (error) {
		console.error('加载侧边栏应用失败:', error);
		appContainer.innerHTML = `
			<div style="padding: 20px; text-align: center; color: #ef4444;">
				<div style="margin-bottom: 10px;">❌</div>
				<div>加载失败，请刷新页面重试</div>
			</div>
		`;
	}
}