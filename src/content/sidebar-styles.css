/* YouTube Clipper 侧边栏样式 - Content Script注入版本 */

/* 确保侧边栏容器样式隔离 */
#yt-clipper-sidebar {
  /* 重置所有可能的继承样式 */
  all: initial;
  
  /* 重新设置必要的样式 */
  position: fixed !important;
  top: 0 !important;
  right: -550px !important;
  width: 550px !important;
  height: 100vh !important;
  background: #f8fafc !important;
  border-left: 1px solid #e2e8f0 !important;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15) !important;
  z-index: 999999 !important;
  transition: right 0.3s ease-in-out !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  color: #1a202c !important;
}

/* 深色模式 */
#yt-clipper-sidebar.dark {
  background: #1a202c !important;
  border-left-color: #4a5568 !important;
  color: #f7fafc !important;
}

/* 侧边栏显示状态 */
#yt-clipper-sidebar.visible {
  right: 0 !important;
}

/* 应用容器 */
#yt-clipper-sidebar-app {
  width: 100% !important;
  height: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 确保内部元素不受YouTube样式影响 */
#yt-clipper-sidebar * {
  box-sizing: border-box !important;
}

/* 滚动条样式 */
#yt-clipper-sidebar::-webkit-scrollbar {
  width: 8px !important;
}

#yt-clipper-sidebar::-webkit-scrollbar-track {
  background: #f1f1f1 !important;
}

#yt-clipper-sidebar::-webkit-scrollbar-thumb {
  background: #c1c1c1 !important;
  border-radius: 4px !important;
}

#yt-clipper-sidebar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8 !important;
}

/* 深色模式滚动条 */
#yt-clipper-sidebar.dark::-webkit-scrollbar-track {
  background: #2d3748 !important;
}

#yt-clipper-sidebar.dark::-webkit-scrollbar-thumb {
  background: #4a5568 !important;
}

#yt-clipper-sidebar.dark::-webkit-scrollbar-thumb:hover {
  background: #718096 !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  #yt-clipper-sidebar {
    width: 100vw !important;
    right: -100vw !important;
  }
  
  #yt-clipper-sidebar.visible {
    right: 0 !important;
  }
}

/* 小屏幕优化 */
@media (max-width: 768px) {
  #yt-clipper-sidebar {
    border-left: none !important;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3) !important;
  }
}

/* 关闭按钮样式 */
.yt-clipper-close-btn {
  position: absolute !important;
  top: 10px !important;
  right: 10px !important;
  width: 32px !important;
  height: 32px !important;
  border: none !important;
  background: rgba(0, 0, 0, 0.1) !important;
  border-radius: 50% !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 18px !important;
  color: #64748b !important;
  transition: all 0.2s ease !important;
  z-index: 1000000 !important;
}

.yt-clipper-close-btn:hover {
  background: rgba(0, 0, 0, 0.2) !important;
  color: #374151 !important;
}

.dark .yt-clipper-close-btn {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #a0aec0 !important;
}

.dark .yt-clipper-close-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  color: #e2e8f0 !important;
}
