// 侧边栏应用入口点 - 用于在content script中动态加载
import SidebarApp from './sidebar-app.svelte';

// 全局变量，用于存储应用实例
let sidebarAppInstance: any = null;

// 初始化侧边栏应用
export function initSidebarApp(target: HTMLElement): any {
	if (sidebarAppInstance) {
		// 如果已存在，先销毁
		sidebarAppInstance.$destroy();
	}

	try {
		sidebarAppInstance = new SidebarApp({
			target: target,
		});
		
		console.log('侧边栏Svelte应用已初始化');
		return sidebarAppInstance;
	} catch (error) {
		console.error('初始化侧边栏应用失败:', error);
		return null;
	}
}

// 销毁侧边栏应用
export function destroySidebarApp() {
	if (sidebarAppInstance) {
		sidebarAppInstance.$destroy();
		sidebarAppInstance = null;
		console.log('侧边栏应用已销毁');
	}
}

// 获取当前应用实例
export function getSidebarApp() {
	return sidebarAppInstance;
}
