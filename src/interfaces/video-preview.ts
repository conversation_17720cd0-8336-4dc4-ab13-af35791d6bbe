/**
 * 视频预览相关接口定义
 * 支持 delogo 区域选择和关键帧捕获功能
 */

/**
 * 预览尺寸接口
 */
export interface IPreviewDimensions {
    width: number;                 // 预览宽度
    height: number;                // 预览高度
    aspectRatio: number;           // 宽高比
    scale: number;                 // 缩放比例
}

/**
 * 视频帧数据接口
 */
export interface IVideoFrame {
    imageData: string;             // Base64编码的图像数据
    width: number;                 // 原始视频宽度
    height: number;                // 原始视频高度
    timestamp: number;             // 时间戳
}

/**
 * 预览状态接口
 */
export interface IPreviewState {
    isLoading: boolean;            // 是否正在加载
    isCapturing: boolean;          // 是否正在捕获帧
    isSelecting: boolean;          // 是否正在选择区域
    duration: number;              // 视频总时长
    currentTime: number;           // 当前播放时间
    videoWidth: number;            // 视频原始宽度
    videoHeight: number;           // 视频原始高度
}

/**
 * 区域选择状态接口
 */
export interface ISelectionState {
    isActive: boolean;             // 是否正在选择
    startX: number;                // 起始X坐标
    startY: number;                // 起始Y坐标
    currentX: number;              // 当前X坐标
    currentY: number;              // 当前Y坐标
    regionName: string;            // 当前区域名称
}

/**
 * 画布配置接口
 */
export interface ICanvasConfig {
    maxWidth: number;              // 最大宽度
    maxHeight: number;             // 最大高度
    captureInterval: number;       // 捕获间隔（毫秒）
    jpegQuality: number;           // JPEG压缩质量（0-1）
}



/**
 * 预览模态配置接口
 */
export interface IPreviewModalConfig {
    videoId: string;               // 视频ID
    tabId: number;                 // 标签页ID
    initialTime?: number;          // 初始播放时间
    blurRegions?: IBlurRegion[];   // 初始模糊区域
    canvasConfig?: ICanvasConfig;  // 画布配置
    blurConfig?: IBlurConfig;      // 模糊配置
}

/**
 * 帧捕获结果接口
 */
export interface ICaptureResult {
    success: boolean;              // 是否成功
    frame?: IVideoFrame;           // 帧数据
    error?: string;                // 错误信息
}

/**
 * 预览事件接口
 */
export interface IPreviewEvents {
    onFrameCapture?: (frame: IVideoFrame) => void;
    onRegionAdd?: (region: IBlurRegion) => void;
    onRegionUpdate?: (region: IBlurRegion) => void;
    onRegionDelete?: (regionId: string) => void;
    onRegionToggle?: (regionId: string, enabled: boolean) => void;
    onPreviewReady?: (dimensions: IPreviewDimensions) => void;
    onError?: (error: string) => void;
}

/**
 * 工具函数类型定义
 */
export type BlurFunction = (
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    width: number,
    height: number,
    intensity: number
) => void;

export type FrameCaptureFunction = () => Promise<ICaptureResult>;

export type DimensionCalculateFunction = (
    videoWidth: number,
    videoHeight: number,
    maxWidth: number,
    maxHeight: number
) => IPreviewDimensions;

/**
 * 默认配置常量
 */
export const DEFAULT_CANVAS_CONFIG: ICanvasConfig = {
    maxWidth: 800,
    maxHeight: 450,
    captureInterval: 100,          // 10 FPS
    jpegQuality: 0.8
};



/**
 * 工具函数：计算预览尺寸
 */
export function calculatePreviewDimensions(
    videoWidth: number,
    videoHeight: number,
    maxWidth: number,
    maxHeight: number
): IPreviewDimensions {
    // 确保输入值有效
    if (!videoWidth || !videoHeight || videoWidth <= 0 || videoHeight <= 0) {
        return {
            width: maxWidth,
            height: maxHeight,
            aspectRatio: 16/9,
            scale: 1
        };
    }

    const aspectRatio = videoWidth / videoHeight;

    let width = videoWidth;
    let height = videoHeight;

    // 按比例缩放以适应最大尺寸，保持宽高比
    const scaleByWidth = maxWidth / videoWidth;
    const scaleByHeight = maxHeight / videoHeight;
    const scale = Math.min(scaleByWidth, scaleByHeight);

    width = videoWidth * scale;
    height = videoHeight * scale;

    return {
        width: Math.floor(width),
        height: Math.floor(height),
        aspectRatio,
        scale
    };
}

/**
 * 工具函数：验证 delogo 区域
 */
export function validateDelogoRegion(region: Partial<IDelogoRegion>, videoWidth: number, videoHeight: number): boolean {
    return !!(
        region.x !== undefined && region.x >= 0 && region.x < videoWidth &&
        region.y !== undefined && region.y >= 0 && region.y < videoHeight &&
        region.width !== undefined && region.width > 0 && (region.x + region.width) <= videoWidth &&
        region.height !== undefined && region.height > 0 && (region.y + region.height) <= videoHeight
    );
}

/**
 * 工具函数：生成区域ID
 */
export function generateRegionId(): string {
    return `region_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}