import type { IVideoClip } from './clip-time';
import type { IDelogoRegion } from '../utils/yt-dlp-generator';

export interface IVideo {
	id: string;
	title: string;
	clips: IVideoClip[];
	// 新增元数据字段，使用可选字段保证向后兼容
	uploader?: string;       // 上传者
	uploadDate?: string;     // 上传日期
	viewCount?: number;      // 播放量
	duration?: number;       // 视频总时长（秒）
	delogoRegions?: IDelogoRegion[]; // 保留视频级别的delogo区域配置（向后兼容）
	videoWidth?: number;     // 视频宽度
	videoHeight?: number;    // 视频高度
}
