import { storageDriver } from '../storage-driver';

chrome.commands.onCommand.addListener(async (command) => {
    if (command === 'play-toggle') {
        playToggle();
        return;
    }
});

// 监听存储变化，动态设置popup
chrome.storage.onChanged.addListener(async (changes, namespace) => {
    if (namespace === 'sync' && changes.preferSidebar) {
        await updateActionPopup();
    }
});

// 扩展启动时设置popup
chrome.runtime.onStartup.addListener(async () => {
    await updateActionPopup();
});

// 扩展安装时设置popup
chrome.runtime.onInstalled.addListener(async () => {
    await updateActionPopup();
});

// 检查浏览器是否支持sidePanel API
function supportsSidePanel(): boolean {
    return typeof chrome !== 'undefined' &&
           chrome.sidePanel &&
           typeof chrome.sidePanel.open === 'function';
}

// 统一方案：动态更新action popup设置
async function updateActionPopup() {
    try {
        const storage = await storageDriver.get();

        // 统一策略：只有在真正支持sidePanel且用户偏好时才启用侧边栏
        // 不支持的浏览器始终使用popup，保持体验一致
        if (storage.preferSidebar && supportsSidePanel()) {
            await chrome.action.setPopup({ popup: '' });
        } else {
            // 不支持或用户不偏好时，使用标准popup
            await chrome.action.setPopup({ popup: 'src/popup/index.html' });
        }
    } catch (error) {
        console.error('Error updating action popup:', error);
    }
}

// 统一的点击事件处理（仅在启用侧边栏时触发）
chrome.action.onClicked.addListener(async (tab) => {
    try {
        const storage = await storageDriver.get();

        // 只有在用户偏好且支持的情况下才会到达这里
        if (storage.preferSidebar && tab.id && supportsSidePanel()) {
            try {
                await chrome.sidePanel.open({ tabId: tab.id });
            } catch (error) {
                console.log('Failed to open sidebar:', error);
                // 失败时打开options页面
                chrome.runtime.openOptionsPage();
            }
        }
    } catch (error) {
        console.error('Error handling action click:', error);
    }
});



async function playToggle() {
    const tabs = await chrome.tabs.query({});
    for (let i = 0; i < tabs.length; i++) {
        const tab = tabs[i];
        const isYoutubeMusic = !!tab.url?.includes('music.youtube.co');
        if (!isYoutubeMusic && !tab.url?.includes('youtube.com/watch?v=')) continue;

        if (isYoutubeMusic) {
            await chrome.scripting.executeScript({
                func: () => {
                    document.dispatchEvent(
                        new KeyboardEvent('keydown', {
                            key: ' ',
                            keyCode: 32,
                            which: 32,
                            shiftKey: false,
                            ctrlKey: false,
                            metaKey: false,
                        })
                    );
                },
                target: {
                    tabId: tab.id!,
                },
            });
            return;
        }

        await chrome.scripting.executeScript({
            func: () => {
                document.dispatchEvent(
                    new KeyboardEvent('keydown', {
                        key: 'k',
                        keyCode: 75,
                        code: 'KeyK',
                        which: 75,
                        shiftKey: false,
                        ctrlKey: false,
                        metaKey: false,
                    })
                );
            },
            target: {
                tabId: tab.id!,
            },
        });
        return;
    }
}

// 初始化popup设置
updateActionPopup();