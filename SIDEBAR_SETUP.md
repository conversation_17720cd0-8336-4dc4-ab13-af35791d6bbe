# YouTube Clipper 侧边栏模式设置指南

## 功能概述
YouTube Clipper 现在支持侧边栏模式，为Chrome核心浏览器（如Edge、Chrome、Brave等）提供更好的用户体验。

## 如何启用侧边栏模式

### 方法1：通过设置页面
1. 右键点击扩展图标，选择"选项"
2. 在设置页面中找到"Interface Preferences"分区
3. 开启"Prefer Sidebar Mode"选项
4. 点击"Save"保存设置

### 方法2：通过popup中的侧边栏按钮
1. 点击扩展图标打开popup
2. 点击右上角的侧边栏图标（两个矩形的图标）
3. 侧边栏将会打开

## 侧边栏模式特点

### 界面优化
- 固定宽度550px，提供更宽敞的操作空间
- 响应式布局，在窄屏幕下自动适应
- 更大的按钮和图标（20px vs 18px）
- 更宽松的按钮间距
- 完整的浅色/深色模式支持

### 自动切换逻辑
- 启用侧边栏偏好后，点击扩展图标将直接打开侧边栏
- 避免popup和侧边栏的冲突
- 在popup模式下检测到用户偏好侧边栏时，会自动切换

## 技术实现

### 权限要求
- 需要`sidePanel`权限（已在manifest中配置）
- 兼容Chrome扩展Manifest V3

### 文件结构
```
src/sidepanel/
├── app.svelte          # 侧边栏应用组件
├── index.html          # 侧边栏HTML入口
├── index.ts            # 侧边栏TypeScript入口
└── index.css           # 侧边栏专用样式
```

### 核心组件
- `MainApp` 组件支持 `isSidebar` 参数
- 背景脚本处理动态popup设置
- 存储驱动支持 `preferSidebar` 用户偏好

## 故障排除

### 侧边栏不显示
1. 确认浏览器支持侧边栏功能（Chrome 114+）
2. 检查扩展权限是否包含`sidePanel`
3. 尝试重新加载扩展
4. 检查浏览器控制台是否有错误信息

### 样式问题
1. 确认CSS文件正确加载
2. 检查深色/浅色模式切换
3. 验证响应式布局在不同屏幕尺寸下的表现

## 开发注意事项

### 构建配置
- 使用@crxjs/vite-plugin进行构建
- 可能需要手动添加sidePanel权限到构建后的manifest.json
- 确保侧边栏CSS文件正确包含在HTML中

### 测试建议
1. 在不同Chrome核心浏览器中测试
2. 验证popup和侧边栏模式的切换
3. 测试用户偏好设置的持久化
4. 检查响应式布局的表现
