import { crx } from "@crxjs/vite-plugin";
import { svelte } from "@sveltejs/vite-plugin-svelte";
import { defineConfig } from "vite";
import manifest from "./src/manifest.config";
import { fileURLToPath, URL } from "url";

// https://vitejs.dev/config/
export default defineConfig({
    plugins: [svelte(), crx({ manifest })],
    server: {
        port: 5173,
        strictPort: true,
        hmr: {
            clientPort: 5173,
        },
    },
    resolve: {
        alias: [
            { find: '@', replacement: fileURLToPath(new URL('./src', import.meta.url)) }
        ]
    },
    build: {
        chunkSizeWarningLimit: 600,
        rollupOptions: {
            output: {
                manualChunks: (id) => {
                    // Firebase相关模块单独打包
                    if (id.includes('firebase')) {
                        return 'firebase';
                    }
                    // Svelte相关模块单独打包
                    if (id.includes('svelte')) {
                        return 'svelte';
                    }
                    // 大型组件单独打包
                    if (id.includes('video-records.svelte') || id.includes('command-preview.svelte')) {
                        return 'video-components';
                    }
                    // 工具函数单独打包
                    if (id.includes('browser-detector.ts') || id.includes('yt-dlp-generator.ts')) {
                        return 'utils';
                    }
                    // 其他第三方库
                    if (id.includes('node_modules')) {
                        return 'vendor';
                    }
                }
            }
        },
        minify: 'esbuild',
        target: 'es2020'
    }
});
