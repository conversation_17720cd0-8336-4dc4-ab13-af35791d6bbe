{"permissions": {"allow": ["Bash(cd \"/Users/<USER>/Desktop/backup/YT-Clipper enhance/youtube-clipper\")", "Bash(npm run build)", "Bash(find \"/Users/<USER>/Desktop/backup/YT-Clipper enhance/youtube-clipper/src/assets\" -type f -exec ls -la {} ;)", "Bash(diff \"/Users/<USER>/Desktop/backup/YT-Clipper enhance/youtube-clipper/src/popup/utils/firebase.ts\" \"/Users/<USER>/Desktop/backup/YT-Clipper enhance/youtube-clipper/src/options/utils/firebase.ts\")", "Bash(find \"/Users/<USER>/Desktop/backup/YT-Clipper enhance/youtube-clipper/src\" -name \"*.svelte\" -exec wc -l {})", "Bash(find \"/Users/<USER>/Desktop/backup/YT-Clipper enhance/youtube-clipper/src\" -name \"*.ts\" -exec wc -l {})", "Bash(diff \"/Users/<USER>/Desktop/backup/YT-Clipper enhance/youtube-clipper/src/popup/stores/user-store.ts\" \"/Users/<USER>/Desktop/backup/YT-Clipper enhance/youtube-clipper/src/options/stores/user-store.ts\")", "Bash(grep -A 2 -B 2 '\"\"\"\"firebase\"\"\"\":' \"/Users/<USER>/Desktop/backup/YT-Clipper enhance/youtube-clipper/package-lock.json\")", "Bash(grep -c '\"\"firebase' \"/Users/<USER>/Desktop/backup/YT-Clipper enhance/youtube-clipper/package-lock.json\")", "Bash(find \"/Users/<USER>/Desktop/backup/YT-Clipper enhance/youtube-clipper/src\" -name \"*.svelte\")", "Bash(find \"/Users/<USER>/Desktop/backup/YT-Clipper enhance/youtube-clipper/src\" -name \"*.ts\")", "Bash(npm install --save-dev terser)"], "deny": []}}