/** @type {import('tailwindcss').Config} */
export default {
	content: ['./src/**/*.{html,js,svelte,ts}'],
	theme: {
		extend: {
			colors: {
				primary: '#2e6d81',
				secondary: '#f74975',
				light: '#f5f7fa',
				dark: '#1a202c',
				color0: '#00ADB5',
				// 轻拟物风格颜色
				'glass-light': 'rgba(255, 255, 255, 0.25)',
				'glass-dark': 'rgba(26, 32, 44, 0.25)',
				'neu-light': '#f5f7fa',
				'neu-dark': '#1a202c',
				'neu-shadow-light': 'rgba(255, 255, 255, 0.8)',
				'neu-shadow-dark': 'rgba(0, 0, 0, 0.3)',
			},
			boxShadow: {
				'neu-inset': 'inset 2px 2px 5px rgba(0, 0, 0, 0.1), inset -2px -2px 5px rgba(255, 255, 255, 0.8)',
				'neu-inset-dark': 'inset 2px 2px 5px rgba(0, 0, 0, 0.3), inset -2px -2px 5px rgba(255, 255, 255, 0.1)',
				'neu-outset': '4px 4px 8px rgba(0, 0, 0, 0.1), -4px -4px 8px rgba(255, 255, 255, 0.8)',
				'neu-outset-dark': '4px 4px 8px rgba(0, 0, 0, 0.3), -4px -4px 8px rgba(255, 255, 255, 0.05)',
				'neu-hover': '6px 6px 12px rgba(0, 0, 0, 0.15), -6px -6px 12px rgba(255, 255, 255, 0.9)',
				'neu-hover-dark': '6px 6px 12px rgba(0, 0, 0, 0.4), -6px -6px 12px rgba(255, 255, 255, 0.08)',
				'glass': '0 8px 32px rgba(31, 38, 135, 0.37)',
				'glass-dark': '0 8px 32px rgba(0, 0, 0, 0.5)',
			},
			backdropBlur: {
				'glass': '10px',
			},
		},
	},
	plugins: [],
	darkMode: 'class',
};
