# YouTube Clipper Enhanced

> 🎬 智能YouTube视频片段收集与yt-dlp批量处理Chrome插件

## 📖 简介

YouTube Clipper Enhanced 是一个功能强大的Chrome插件，专为YouTube视频片段的精确收集、智能命名和批量处理而设计。通过直观的用户界面收集片段信息，自动生成专业的yt-dlp命令，实现视频片段的批量下载、重命名和后处理。

## ✨ 主要特性

- 🎯 **智能片段收集**：一键获取YouTube当前播放时间，精确设置片段开始/结束点
- 📝 **自定义片段命名**：为每个片段设置个性化名称
- 🤖 **智能文件命名**：自动生成包含片段名称、时长、日期、视频ID、上传者、播放量的文件名
- 📁 **灵活路径配置**：支持自定义下载路径，批量下载自动创建"视频标题+时间"文件夹
- 🛠️ **yt-dlp命令生成**：基于收集数据自动生成完整的yt-dlp命令
- 🎨 **高级视频处理**：支持FFmpeg滤镜（局部高斯模糊、裁剪等），配置可保存到片段记录
- 🖼️ **视频预览窗口**：实时预览模糊效果，可视化区域选择工具
- ☁️ **云同步支持**：Firebase云端数据同步，跨设备使用
- 🌙 **现代化UI**：支持深色/浅色主题，响应式设计
- 🚀 **一键安装指南**：内置yt-dlp和FFmpeg安装说明，支持Windows/Mac/Linux

## 🚀 快速开始

### 环境要求

- Node.js 16+
- Chrome浏览器
- yt-dlp命令行工具
- FFmpeg（用于视频处理）

### 首次使用安装指南

#### Windows 用户
```bash
# 安装 yt-dlp
winget install yt-dlp

# 安装 FFmpeg
winget install FFmpeg

# 或者使用 Chocolatey
choco install yt-dlp ffmpeg
```

#### Mac 用户
```bash
# 使用 Homebrew 安装
brew install yt-dlp ffmpeg

# 或者使用 MacPorts
sudo port install yt-dlp ffmpeg
```

#### Linux 用户
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install yt-dlp ffmpeg

# CentOS/RHEL/Fedora
sudo dnf install yt-dlp ffmpeg
```

### 插件安装步骤

```bash
# 1. 克隆项目
git clone https://github.com/ylfyt/youtube-clipper.git
cd youtube-clipper

# 2. 安装依赖
yarn install

# 3. 开发模式
yarn dev

# 4. 构建生产版本
yarn build
```

### Chrome插件加载

1. 打开Chrome扩展管理页面 `chrome://extensions/`
2. 启用"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择项目的`dist`目录

## 🎮 使用方法

### 基础使用流程

1. **打开YouTube视频**，找到需要剪辑的片段
2. **启动插件**，自动识别当前视频
3. **设置片段**：
   - 播放到开始位置，点击"获取当前时间"设置开始点
   - 播放到结束位置，点击"获取当前时间"设置结束点
   - 输入片段自定义名称
4. **添加多个片段**（可选）
5. **生成yt-dlp命令**：插件自动生成完整命令
6. **复制并执行**：一键复制命令到终端执行

### 文件命名示例

生成的文件将按以下格式命名：
```
精彩开场_45s_20241218_dQw4w9WgXcQ_20091025_RickAstleyVEVO_1.2B.mp4
搞笑片段_30s_20241218_dQw4w9WgXcQ_20091025_RickAstleyVEVO_1.2B.mp4
结尾彩蛋_60s_20241218_dQw4w9WgXcQ_20091025_RickAstleyVEVO_1.2B.mp4
```

格式说明：`[片段名称]_[时长]s_[下载日期]_[视频ID]_[上传日期]_[上传者]_[播放量].mp4`

## 🔧 技术架构

### 前端技术栈
- **框架**：Svelte + TypeScript
- **构建工具**：Vite + @crxjs/vite-plugin
- **样式**：Tailwind CSS
- **状态管理**：Svelte Stores
- **云同步**：Firebase Firestore

### 核心功能模块
- **Popup界面**：主要用户交互界面
- **Content Script**：YouTube页面注入脚本，获取播放时间
- **Background Script**：后台服务处理
- **Options页面**：高级设置配置

## 📋 开发计划

### ✅ 已完成功能 (85%)
- 基础Chrome插件架构
- 片段时间管理系统
- 数据存储与云同步
- 现代化用户界面
- 基础播放控制功能
- **片段自定义命名系统**
- **自动时间戳获取功能** ⭐
- **视频片段精确跳转系统** ⭐
  - 支持从剪藏列表直接跳转到视频特定时间点
  - 实现带时间戳的精确跳转功能
  - 优化片段预览和跳转用户界面

### 🔄 开发中功能 (35%)
- YouTube元数据收集⭐
- yt-dlp命令生成器⭐
- 视频预览窗口与区域选择
- 高级FFmpeg滤镜支持


### 🎯 未来规划
- 预设模板管理系统
- 批量处理优化
- 快捷键支持
- 多语言支持
- 增强片段管理功能
  - 片段缩略图预览
  - 片段时间轴可视化
  - 快速跳转按钮优化

## 🛠️ yt-dlp命令示例


## 📚 相关资源

- [yt-dlp官方文档](https://github.com/yt-dlp/yt-dlp)
- [FFmpeg滤镜文档](https://ffmpeg.org/ffmpeg-filters.html)
- [Chrome扩展开发指南](https://developer.chrome.com/docs/extensions/)
- [YouTube Data API](https://developers.google.com/youtube/v3)
- [详细技术文档](.voidrules/README.md)


为YouTube Clipper记录页面实现展开/收起功能优化：

**核心需求：**
为每个父级视频记录添加展开/收起按钮，解决记录过多时查找困难的问题。

**按钮设计规范：**
- 位置：视频卡片左边缘外侧，垂直居中对齐
- 样式：箭头形状图标，简约直观设计
- 尺寸：小尺寸设计（建议24x24px），确保不影响现有布局
- 状态：按钮位置固定，不随展开/收起状态移动

**功能行为：**
- 默认状态：所有视频记录默认为收起状态
- 独立控制：每个视频的展开/收起状态独立，互不影响
- 显示条件：只有包含片段的视频才显示展开/收起按钮

**动画要求：**
- 展开/收起过渡：使用平滑动画，避免生硬切换
- 箭头旋转：展开时箭头旋转90度指示状态变化
- 片段入场动画：展开时片段卡片从右往左逐个递进入场，营造拟物化动效
- 动画时长：建议0.3-0.4秒，使用适当的缓动函数

**技术要求：**
- 状态管理：使用响应式状态管理展开/收起状态
- 样式一致性：保持与现有neumorphic（拟物）UI风格一致
- 暗色模式：确保在暗色模式下正常显示
- 性能优化：使用CSS动画而非JavaScript动画

**构建流程：**
1. 编码完成后，先清理构建缓存（删除dist目录和node_modules/.vite）
2. 执行构建命令验证功能正常
3. 确保TypeScript类型检查通过
4. 验证在不同主题模式下的显示效果

**文件修改范围：**
主要修改 `src/popup/pages/video-records.svelte` 文件，添加状态管理、HTML结构调整和CSS动画样式。